// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00F0A412FD83A03BEBE9754593CD605A /* Pods-private_Tests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B081919EDED25D6A24B1A21BDD8457B /* Pods-private_Tests-dummy.m */; };
		1D6DFCFC2B9B268D66814F9D358EA63D /* Pods-private_Example-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = EA313EF1BEE41849249C8030F8483CFD /* Pods-private_Example-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		242892102A37E1AA38D5767D210059DE /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 271294556DC8976B10A88F1119AEB52A /* Foundation.framework */; };
		24F6CDB07E40DEA0AA1E955D31887CBA /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 271294556DC8976B10A88F1119AEB52A /* Foundation.framework */; };
		27D13BFB6C5E9715C5920E6A45F0BA6E /* Pods-private_Tests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 499DB8955A9F7EAE9E5090E42B1F7AFF /* Pods-private_Tests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		309199898AFB6B1A10A72C86B4BB92BA /* MapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8FA3E110E447CB42F61B792CB92D0FB4 /* MapKit.framework */; };
		6B5EDDC24A0774C3B553B4D9073ADD49 /* FirstViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BE1DF2797D424575761148E86609A8A /* FirstViewController.m */; };
		82DA82B23A0CD5FC45A7BF29498B025C /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 48CAD5976D5174FB60D4154C4F819FFF /* UIKit.framework */; };
		89D1023F25852265E759D3E2053DADA9 /* Pods-private_Example-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 748B9FAE4FD0B0DC6AA23CBAAF83B063 /* Pods-private_Example-dummy.m */; };
		8B9224781EE3543DDACD2188D95BFDF9 /* Asset.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0BD507EF48FFDCB2475629A71E1F62E5 /* Asset.xcassets */; };
		90535E8667658F45AD661A276F3A598D /* private-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = E932CF2CD1AA61C5BB0143B81FB3B90C /* private-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A0A760D13B3B909464C88F8A5B9F0DA3 /* private-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 751910D99D514EA63C349594E13E473C /* private-dummy.m */; };
		AA290B69211770AAA2280FAD6300A493 /* FirstViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = ED4177376D6421C81C5C928432060D92 /* FirstViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CDA47D6FC8E3E423C6C354942FC6056A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 271294556DC8976B10A88F1119AEB52A /* Foundation.framework */; };
		F487C66999DB770270231F744931776C /* private-private in Resources */ = {isa = PBXBuildFile; fileRef = 223C0BF9D554200BDD5680E231B2BA34 /* private-private */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		14459829E32E5AE771C7E0D75C4744CF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 34B4FB36DFF343EF119641029C3C46FD;
			remoteInfo = "private-private";
		};
		CFBE4CBD4B0C5FBE6FDB7A8DEE04647A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 75DE2C2D4641777618376B52A56D2E99;
			remoteInfo = private;
		};
		E097304F827319B8B6BF0CE0264656DC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 192D84E1BAC513CBEDA47A58F0193B96;
			remoteInfo = "Pods-private_Example";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0BD507EF48FFDCB2475629A71E1F62E5 /* Asset.xcassets */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = folder.assetcatalog; name = Asset.xcassets; path = private/Assets/Images/Asset.xcassets; sourceTree = "<group>"; };
		1441D920938EC539AD1CE8778B766408 /* Pods-private_Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-private_Tests.debug.xcconfig"; sourceTree = "<group>"; };
		223C0BF9D554200BDD5680E231B2BA34 /* private-private */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "private-private"; path = private.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		271294556DC8976B10A88F1119AEB52A /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		272DB3A54A1A0C91F21138DE1A2699DD /* private.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = private.debug.xcconfig; sourceTree = "<group>"; };
		2934FF296B2E2A8FE5D30486CD6C3600 /* Pods-private_Tests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-private_Tests"; path = Pods_private_Tests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		29794DB9D9E3E72513273CFC740B6F77 /* Pods-private_Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-private_Example.release.xcconfig"; sourceTree = "<group>"; };
		2FB6C724EABAC471A37FD0008A3A7C2F /* private.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = private.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		31BE80AB72B5BC5D3825BBDA7A86B272 /* private-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "private-prefix.pch"; sourceTree = "<group>"; };
		348FA11F1D273221DEDA50026F696572 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; path = LICENSE; sourceTree = "<group>"; };
		3B84DE44A66F3AD4829F48F4E81E1B69 /* private-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "private-Info.plist"; sourceTree = "<group>"; };
		48CAD5976D5174FB60D4154C4F819FFF /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		499DB8955A9F7EAE9E5090E42B1F7AFF /* Pods-private_Tests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-private_Tests-umbrella.h"; sourceTree = "<group>"; };
		4D7FA2679BFB3301801CE114FFF393FC /* Pods-private_Example-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-private_Example-acknowledgements.plist"; sourceTree = "<group>"; };
		5B91ED29927AEF98BFB865954E485F47 /* Pods-private_Tests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-private_Tests-acknowledgements.markdown"; sourceTree = "<group>"; };
		706AFD622BBD33EAA5495D74377792F1 /* Pods-private_Example-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-private_Example-Info.plist"; sourceTree = "<group>"; };
		747ECCCC8E9A14377AF853B6098FD9B0 /* Pods-private_Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-private_Tests.release.xcconfig"; sourceTree = "<group>"; };
		748B9FAE4FD0B0DC6AA23CBAAF83B063 /* Pods-private_Example-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-private_Example-dummy.m"; sourceTree = "<group>"; };
		751910D99D514EA63C349594E13E473C /* private-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "private-dummy.m"; sourceTree = "<group>"; };
		79F93184298B63DDF53FC4429136640F /* private.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = private.release.xcconfig; sourceTree = "<group>"; };
		89ABC5C53CFAAC0A4A0E5EF3F21B034F /* ResourceBundle-private-private-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-private-private-Info.plist"; sourceTree = "<group>"; };
		8BE1DF2797D424575761148E86609A8A /* FirstViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FirstViewController.m; path = private/Classes/ViewController/FirstViewController.m; sourceTree = "<group>"; };
		8DF8810C476BF423CD26673C480CEF29 /* Pods-private_Example */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-private_Example"; path = Pods_private_Example.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8FA3E110E447CB42F61B792CB92D0FB4 /* MapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MapKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/MapKit.framework; sourceTree = DEVELOPER_DIR; };
		9274D5349B73EE98F2451796357E0DDA /* Pods-private_Example-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-private_Example-acknowledgements.markdown"; sourceTree = "<group>"; };
		9B081919EDED25D6A24B1A21BDD8457B /* Pods-private_Tests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-private_Tests-dummy.m"; sourceTree = "<group>"; };
		9BDF07B556B019E4EDBFF3243F505FC9 /* Pods-private_Tests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-private_Tests.modulemap"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A499ED29A3E71AA661E8BD85E064A08F /* private */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = private; path = private.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B86D513E7815C897B2D499124F7F72D5 /* Pods-private_Example-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-private_Example-frameworks.sh"; sourceTree = "<group>"; };
		C62C200CCB1732B0ACB9B83A83F96573 /* Pods-private_Example.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-private_Example.modulemap"; sourceTree = "<group>"; };
		D2AD2A43CAAF0BFD30CECFF61B93CDA7 /* Pods-private_Tests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-private_Tests-Info.plist"; sourceTree = "<group>"; };
		D717556745580A533DA238D2CF032BD3 /* private.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = private.modulemap; sourceTree = "<group>"; };
		DB04E7CD62D115F913EC1CEE7AD38991 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; path = README.md; sourceTree = "<group>"; };
		DD2B02C9330029673048230C2375ED0D /* Pods-private_Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-private_Example.debug.xcconfig"; sourceTree = "<group>"; };
		E932CF2CD1AA61C5BB0143B81FB3B90C /* private-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "private-umbrella.h"; sourceTree = "<group>"; };
		EA313EF1BEE41849249C8030F8483CFD /* Pods-private_Example-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-private_Example-umbrella.h"; sourceTree = "<group>"; };
		ED4177376D6421C81C5C928432060D92 /* FirstViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FirstViewController.h; path = private/Classes/ViewController/FirstViewController.h; sourceTree = "<group>"; };
		F1FBA041753E3DFDF59DF9B183207A41 /* Pods-private_Tests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-private_Tests-acknowledgements.plist"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0C3754421EF34CDB9548E274C786EEBB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				24F6CDB07E40DEA0AA1E955D31887CBA /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		11B213EF411972D34E6FFBD669C2816E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				242892102A37E1AA38D5767D210059DE /* Foundation.framework in Frameworks */,
				309199898AFB6B1A10A72C86B4BB92BA /* MapKit.framework in Frameworks */,
				82DA82B23A0CD5FC45A7BF29498B025C /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D5BDFA4F2CAFC7029A4E1E39350F71B7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CDA47D6FC8E3E423C6C354942FC6056A /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D6814EFD9EDC97323E067F69EF364466 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		03C5C200A0787E300053CFA8F53CA094 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AE54FD7F3666F817BF714531B7259D86 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		0A4199FE2F12F5C52F6C398DCC196A69 /* private */ = {
			isa = PBXGroup;
			children = (
				0BD507EF48FFDCB2475629A71E1F62E5 /* Asset.xcassets */,
				ED4177376D6421C81C5C928432060D92 /* FirstViewController.h */,
				8BE1DF2797D424575761148E86609A8A /* FirstViewController.m */,
				102E9DBA8E82DE5E964A5581C968DA88 /* Pod */,
				E6CB16CB815896657D9DF4F7FBD35506 /* Support Files */,
			);
			name = private;
			path = ../..;
			sourceTree = "<group>";
		};
		102E9DBA8E82DE5E964A5581C968DA88 /* Pod */ = {
			isa = PBXGroup;
			children = (
				348FA11F1D273221DEDA50026F696572 /* LICENSE */,
				2FB6C724EABAC471A37FD0008A3A7C2F /* private.podspec */,
				DB04E7CD62D115F913EC1CEE7AD38991 /* README.md */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		30427D3A20436144E3A1E8EFB4B29A7D /* Pods-private_Tests */ = {
			isa = PBXGroup;
			children = (
				9BDF07B556B019E4EDBFF3243F505FC9 /* Pods-private_Tests.modulemap */,
				5B91ED29927AEF98BFB865954E485F47 /* Pods-private_Tests-acknowledgements.markdown */,
				F1FBA041753E3DFDF59DF9B183207A41 /* Pods-private_Tests-acknowledgements.plist */,
				9B081919EDED25D6A24B1A21BDD8457B /* Pods-private_Tests-dummy.m */,
				D2AD2A43CAAF0BFD30CECFF61B93CDA7 /* Pods-private_Tests-Info.plist */,
				499DB8955A9F7EAE9E5090E42B1F7AFF /* Pods-private_Tests-umbrella.h */,
				1441D920938EC539AD1CE8778B766408 /* Pods-private_Tests.debug.xcconfig */,
				747ECCCC8E9A14377AF853B6098FD9B0 /* Pods-private_Tests.release.xcconfig */,
			);
			name = "Pods-private_Tests";
			path = "Target Support Files/Pods-private_Tests";
			sourceTree = "<group>";
		};
		6338E56887DCC07F9BEB81AB027B1CC5 /* Pods-private_Example */ = {
			isa = PBXGroup;
			children = (
				C62C200CCB1732B0ACB9B83A83F96573 /* Pods-private_Example.modulemap */,
				9274D5349B73EE98F2451796357E0DDA /* Pods-private_Example-acknowledgements.markdown */,
				4D7FA2679BFB3301801CE114FFF393FC /* Pods-private_Example-acknowledgements.plist */,
				748B9FAE4FD0B0DC6AA23CBAAF83B063 /* Pods-private_Example-dummy.m */,
				B86D513E7815C897B2D499124F7F72D5 /* Pods-private_Example-frameworks.sh */,
				706AFD622BBD33EAA5495D74377792F1 /* Pods-private_Example-Info.plist */,
				EA313EF1BEE41849249C8030F8483CFD /* Pods-private_Example-umbrella.h */,
				DD2B02C9330029673048230C2375ED0D /* Pods-private_Example.debug.xcconfig */,
				29794DB9D9E3E72513273CFC740B6F77 /* Pods-private_Example.release.xcconfig */,
			);
			name = "Pods-private_Example";
			path = "Target Support Files/Pods-private_Example";
			sourceTree = "<group>";
		};
		6F7A14C2028DDB20F5621E60B8761CE0 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				0A4199FE2F12F5C52F6C398DCC196A69 /* private */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		7F1715D386F2CF77656D07AFC4DF1359 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				6338E56887DCC07F9BEB81AB027B1CC5 /* Pods-private_Example */,
				30427D3A20436144E3A1E8EFB4B29A7D /* Pods-private_Tests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		8726185C4EA91E0F71B207A1D47D9FC9 /* Products */ = {
			isa = PBXGroup;
			children = (
				8DF8810C476BF423CD26673C480CEF29 /* Pods-private_Example */,
				2934FF296B2E2A8FE5D30486CD6C3600 /* Pods-private_Tests */,
				A499ED29A3E71AA661E8BD85E064A08F /* private */,
				223C0BF9D554200BDD5680E231B2BA34 /* private-private */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AE54FD7F3666F817BF714531B7259D86 /* iOS */ = {
			isa = PBXGroup;
			children = (
				271294556DC8976B10A88F1119AEB52A /* Foundation.framework */,
				8FA3E110E447CB42F61B792CB92D0FB4 /* MapKit.framework */,
				48CAD5976D5174FB60D4154C4F819FFF /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				6F7A14C2028DDB20F5621E60B8761CE0 /* Development Pods */,
				03C5C200A0787E300053CFA8F53CA094 /* Frameworks */,
				8726185C4EA91E0F71B207A1D47D9FC9 /* Products */,
				7F1715D386F2CF77656D07AFC4DF1359 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		E6CB16CB815896657D9DF4F7FBD35506 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				D717556745580A533DA238D2CF032BD3 /* private.modulemap */,
				751910D99D514EA63C349594E13E473C /* private-dummy.m */,
				3B84DE44A66F3AD4829F48F4E81E1B69 /* private-Info.plist */,
				31BE80AB72B5BC5D3825BBDA7A86B272 /* private-prefix.pch */,
				E932CF2CD1AA61C5BB0143B81FB3B90C /* private-umbrella.h */,
				272DB3A54A1A0C91F21138DE1A2699DD /* private.debug.xcconfig */,
				79F93184298B63DDF53FC4429136640F /* private.release.xcconfig */,
				89ABC5C53CFAAC0A4A0E5EF3F21B034F /* ResourceBundle-private-private-Info.plist */,
			);
			name = "Support Files";
			path = "Example/Pods/Target Support Files/private";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		26B746FF0A1169B441AC6AECB3FAB38A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1D6DFCFC2B9B268D66814F9D358EA63D /* Pods-private_Example-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8756A46DD7B1D31D26C4F067D41ABFA /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AA290B69211770AAA2280FAD6300A493 /* FirstViewController.h in Headers */,
				90535E8667658F45AD661A276F3A598D /* private-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E938646CF5876395BB052AF9C9E4E40E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				27D13BFB6C5E9715C5920E6A45F0BA6E /* Pods-private_Tests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		192D84E1BAC513CBEDA47A58F0193B96 /* Pods-private_Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1D64B31622992A503BA697F098368014 /* Build configuration list for PBXNativeTarget "Pods-private_Example" */;
			buildPhases = (
				26B746FF0A1169B441AC6AECB3FAB38A /* Headers */,
				9613333B8C7EB489402F4D1D7C091260 /* Sources */,
				D5BDFA4F2CAFC7029A4E1E39350F71B7 /* Frameworks */,
				3D26BCED5F1068A1CC465761BBA2101D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C4E4A287BCAD4F2C62FA1197D7DE8569 /* PBXTargetDependency */,
			);
			name = "Pods-private_Example";
			productName = Pods_private_Example;
			productReference = 8DF8810C476BF423CD26673C480CEF29 /* Pods-private_Example */;
			productType = "com.apple.product-type.framework";
		};
		34B4FB36DFF343EF119641029C3C46FD /* private-private */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 744B332E535BF1A517FBDC96CF148B6F /* Build configuration list for PBXNativeTarget "private-private" */;
			buildPhases = (
				FEE40FB20CE842AF132239823C9D254C /* Sources */,
				D6814EFD9EDC97323E067F69EF364466 /* Frameworks */,
				0322D9CB378A0294B084BA1E113F4F65 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "private-private";
			productName = private;
			productReference = 223C0BF9D554200BDD5680E231B2BA34 /* private-private */;
			productType = "com.apple.product-type.bundle";
		};
		363E3E4AB405E864D1E9BDB1FCB48B04 /* Pods-private_Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D0E0679BBABA8B5B8245DE983EAAF6C4 /* Build configuration list for PBXNativeTarget "Pods-private_Tests" */;
			buildPhases = (
				E938646CF5876395BB052AF9C9E4E40E /* Headers */,
				220AB9D021F2422D3F4D0F286ED23D64 /* Sources */,
				0C3754421EF34CDB9548E274C786EEBB /* Frameworks */,
				8898EABA165EC06090A13AD57D10AF03 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7678C094C3C27F00C97786CAAEAB25B7 /* PBXTargetDependency */,
			);
			name = "Pods-private_Tests";
			productName = Pods_private_Tests;
			productReference = 2934FF296B2E2A8FE5D30486CD6C3600 /* Pods-private_Tests */;
			productType = "com.apple.product-type.framework";
		};
		75DE2C2D4641777618376B52A56D2E99 /* private */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3F09170C9622862A77E2B387C09FDD9B /* Build configuration list for PBXNativeTarget "private" */;
			buildPhases = (
				E8756A46DD7B1D31D26C4F067D41ABFA /* Headers */,
				C396106E6B61F29C11EB3294E9FD02AD /* Sources */,
				11B213EF411972D34E6FFBD669C2816E /* Frameworks */,
				38BA4E7B16847772E5EF4A56A23EF876 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				20D6649EB1CB82CEC72A3AE5C9CE3AC9 /* PBXTargetDependency */,
			);
			name = private;
			productName = private;
			productReference = A499ED29A3E71AA661E8BD85E064A08F /* private */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8726185C4EA91E0F71B207A1D47D9FC9 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				192D84E1BAC513CBEDA47A58F0193B96 /* Pods-private_Example */,
				363E3E4AB405E864D1E9BDB1FCB48B04 /* Pods-private_Tests */,
				75DE2C2D4641777618376B52A56D2E99 /* private */,
				34B4FB36DFF343EF119641029C3C46FD /* private-private */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0322D9CB378A0294B084BA1E113F4F65 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8B9224781EE3543DDACD2188D95BFDF9 /* Asset.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		38BA4E7B16847772E5EF4A56A23EF876 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F487C66999DB770270231F744931776C /* private-private in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3D26BCED5F1068A1CC465761BBA2101D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8898EABA165EC06090A13AD57D10AF03 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		220AB9D021F2422D3F4D0F286ED23D64 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00F0A412FD83A03BEBE9754593CD605A /* Pods-private_Tests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9613333B8C7EB489402F4D1D7C091260 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				89D1023F25852265E759D3E2053DADA9 /* Pods-private_Example-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C396106E6B61F29C11EB3294E9FD02AD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B5EDDC24A0774C3B553B4D9073ADD49 /* FirstViewController.m in Sources */,
				A0A760D13B3B909464C88F8A5B9F0DA3 /* private-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FEE40FB20CE842AF132239823C9D254C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		20D6649EB1CB82CEC72A3AE5C9CE3AC9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "private-private";
			target = 34B4FB36DFF343EF119641029C3C46FD /* private-private */;
			targetProxy = 14459829E32E5AE771C7E0D75C4744CF /* PBXContainerItemProxy */;
		};
		7678C094C3C27F00C97786CAAEAB25B7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-private_Example";
			target = 192D84E1BAC513CBEDA47A58F0193B96 /* Pods-private_Example */;
			targetProxy = E097304F827319B8B6BF0CE0264656DC /* PBXContainerItemProxy */;
		};
		C4E4A287BCAD4F2C62FA1197D7DE8569 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = private;
			target = 75DE2C2D4641777618376B52A56D2E99 /* private */;
			targetProxy = CFBE4CBD4B0C5FBE6FDB7A8DEE04647A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		14AB66D6EE4376DE6B00171BC6518068 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 272DB3A54A1A0C91F21138DE1A2699DD /* private.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/private";
				IBSC_MODULE = private;
				INFOPLIST_FILE = "Target Support Files/private/ResourceBundle-private-private-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				PRODUCT_NAME = private;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		236109C45490E0A0F48F565B96F481A3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 79F93184298B63DDF53FC4429136640F /* private.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/private";
				IBSC_MODULE = private;
				INFOPLIST_FILE = "Target Support Files/private/ResourceBundle-private-private-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				PRODUCT_NAME = private;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		3377F084A91EB9FB91A4917F99822BAA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 747ECCCC8E9A14377AF853B6098FD9B0 /* Pods-private_Tests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-private_Tests/Pods-private_Tests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-private_Tests/Pods-private_Tests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		60560D49A025E395445179580C30147E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 272DB3A54A1A0C91F21138DE1A2699DD /* private.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/private/private-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/private/private-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/private/private.modulemap";
				PRODUCT_MODULE_NAME = private;
				PRODUCT_NAME = private;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		6402A75EA290A5A63FC4273FB8A0B79A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 79F93184298B63DDF53FC4429136640F /* private.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/private/private-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/private/private-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/private/private.modulemap";
				PRODUCT_MODULE_NAME = private;
				PRODUCT_NAME = private;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		7EE7A78859F657F6BEFC651185B43192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		A8BFC510E952070F0A5CA00F87BF9833 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1441D920938EC539AD1CE8778B766408 /* Pods-private_Tests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-private_Tests/Pods-private_Tests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-private_Tests/Pods-private_Tests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		AE88819ABE7CB2AC823C5FF9543BB961 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DD2B02C9330029673048230C2375ED0D /* Pods-private_Example.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-private_Example/Pods-private_Example-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-private_Example/Pods-private_Example.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B5B693371519FFB78567DC2C37E3E392 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 29794DB9D9E3E72513273CFC740B6F77 /* Pods-private_Example.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-private_Example/Pods-private_Example-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-private_Example/Pods-private_Example.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		D299434AB35E7FD6F7921C8EF24742FF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1D64B31622992A503BA697F098368014 /* Build configuration list for PBXNativeTarget "Pods-private_Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AE88819ABE7CB2AC823C5FF9543BB961 /* Debug */,
				B5B693371519FFB78567DC2C37E3E392 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3F09170C9622862A77E2B387C09FDD9B /* Build configuration list for PBXNativeTarget "private" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				60560D49A025E395445179580C30147E /* Debug */,
				6402A75EA290A5A63FC4273FB8A0B79A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D299434AB35E7FD6F7921C8EF24742FF /* Debug */,
				7EE7A78859F657F6BEFC651185B43192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		744B332E535BF1A517FBDC96CF148B6F /* Build configuration list for PBXNativeTarget "private-private" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				14AB66D6EE4376DE6B00171BC6518068 /* Debug */,
				236109C45490E0A0F48F565B96F481A3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D0E0679BBABA8B5B8245DE983EAAF6C4 /* Build configuration list for PBXNativeTarget "Pods-private_Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8BFC510E952070F0A5CA00F87BF9833 /* Debug */,
				3377F084A91EB9FB91A4917F99822BAA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
