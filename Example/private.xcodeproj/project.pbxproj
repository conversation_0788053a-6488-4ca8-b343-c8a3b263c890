// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		6003F58E195388D20070C39A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F58D195388D20070C39A /* Foundation.framework */; };
		6003F590195388D20070C39A /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F58F195388D20070C39A /* CoreGraphics.framework */; };
		6003F592195388D20070C39A /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F591195388D20070C39A /* UIKit.framework */; };
		6003F598195388D20070C39A /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6003F596195388D20070C39A /* InfoPlist.strings */; };
		6003F59A195388D20070C39A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F599195388D20070C39A /* main.m */; };
		6003F59E195388D20070C39A /* HSWAppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F59D195388D20070C39A /* HSWAppDelegate.m */; };
		6003F5A7195388D20070C39A /* HSWViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F5A6195388D20070C39A /* HSWViewController.m */; };
		6003F5A9195388D20070C39A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 6003F5A8195388D20070C39A /* Images.xcassets */; };
		6003F5B0195388D20070C39A /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F5AF195388D20070C39A /* XCTest.framework */; };
		6003F5B1195388D20070C39A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F58D195388D20070C39A /* Foundation.framework */; };
		6003F5B2195388D20070C39A /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F591195388D20070C39A /* UIKit.framework */; };
		6003F5BA195388D20070C39A /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6003F5B8195388D20070C39A /* InfoPlist.strings */; };
		6003F5BC195388D20070C39A /* Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F5BB195388D20070C39A /* Tests.m */; };
		71719F9F1E33DC2100824A3D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 71719F9D1E33DC2100824A3D /* LaunchScreen.storyboard */; };
		873B8AEB1B1F5CCA007FD442 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 873B8AEA1B1F5CCA007FD442 /* Main.storyboard */; };
		B90B85CEE095FFB64846BEFB /* Pods_private_Tests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 05095CCA7C4490E6D0559D98 /* Pods_private_Tests.framework */; };
		DC2FA26D5EB17B3A10D8D486 /* Pods_private_Example.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A5382BD1AB1FC0382F193D4B /* Pods_private_Example.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		6003F5B3195388D20070C39A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6003F582195388D10070C39A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6003F589195388D20070C39A;
			remoteInfo = private;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		05095CCA7C4490E6D0559D98 /* Pods_private_Tests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_private_Tests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		27FA7A80260337FABAA8BC57 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = net.daringfireball.markdown; name = README.md; path = ../README.md; sourceTree = "<group>"; };
		40254962E09FF425E4BFA705 /* Pods-private_Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-private_Example.debug.xcconfig"; path = "Target Support Files/Pods-private_Example/Pods-private_Example.debug.xcconfig"; sourceTree = "<group>"; };
		4436A5884454AE3826D63453 /* Pods-private_Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-private_Tests.release.xcconfig"; path = "Target Support Files/Pods-private_Tests/Pods-private_Tests.release.xcconfig"; sourceTree = "<group>"; };
		5A457BC08C603F8F4EDF7E7E /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; name = LICENSE; path = ../LICENSE; sourceTree = "<group>"; };
		6003F58A195388D20070C39A /* private_Example.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = private_Example.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6003F58D195388D20070C39A /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		6003F58F195388D20070C39A /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		6003F591195388D20070C39A /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		6003F595195388D20070C39A /* private-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "private-Info.plist"; sourceTree = "<group>"; };
		6003F597195388D20070C39A /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6003F599195388D20070C39A /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		6003F59B195388D20070C39A /* private-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "private-Prefix.pch"; sourceTree = "<group>"; };
		6003F59C195388D20070C39A /* HSWAppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HSWAppDelegate.h; sourceTree = "<group>"; };
		6003F59D195388D20070C39A /* HSWAppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HSWAppDelegate.m; sourceTree = "<group>"; };
		6003F5A5195388D20070C39A /* HSWViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HSWViewController.h; sourceTree = "<group>"; };
		6003F5A6195388D20070C39A /* HSWViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HSWViewController.m; sourceTree = "<group>"; };
		6003F5A8195388D20070C39A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		6003F5AE195388D20070C39A /* private_Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = private_Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		6003F5AF195388D20070C39A /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		6003F5B7195388D20070C39A /* Tests-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Tests-Info.plist"; sourceTree = "<group>"; };
		6003F5B9195388D20070C39A /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6003F5BB195388D20070C39A /* Tests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Tests.m; sourceTree = "<group>"; };
		606FC2411953D9B200FFA9A0 /* Tests-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Tests-Prefix.pch"; sourceTree = "<group>"; };
		63A64C17E848147010CC9E90 /* Pods-private_Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-private_Example.release.xcconfig"; path = "Target Support Files/Pods-private_Example/Pods-private_Example.release.xcconfig"; sourceTree = "<group>"; };
		71719F9E1E33DC2100824A3D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		873B8AEA1B1F5CCA007FD442 /* Main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = Main.storyboard; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		A5382BD1AB1FC0382F193D4B /* Pods_private_Example.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_private_Example.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C032EC2197AE60559A2D1D66 /* private.podspec */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; name = private.podspec; path = ../private.podspec; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		F4D83241F9260C52700B8211 /* Pods-private_Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-private_Tests.debug.xcconfig"; path = "Target Support Files/Pods-private_Tests/Pods-private_Tests.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6003F587195388D20070C39A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F590195388D20070C39A /* CoreGraphics.framework in Frameworks */,
				6003F592195388D20070C39A /* UIKit.framework in Frameworks */,
				6003F58E195388D20070C39A /* Foundation.framework in Frameworks */,
				DC2FA26D5EB17B3A10D8D486 /* Pods_private_Example.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6003F5AB195388D20070C39A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F5B0195388D20070C39A /* XCTest.framework in Frameworks */,
				6003F5B2195388D20070C39A /* UIKit.framework in Frameworks */,
				6003F5B1195388D20070C39A /* Foundation.framework in Frameworks */,
				B90B85CEE095FFB64846BEFB /* Pods_private_Tests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4EF402BA1F855CA7ABF5B713 /* Pods */ = {
			isa = PBXGroup;
			children = (
				40254962E09FF425E4BFA705 /* Pods-private_Example.debug.xcconfig */,
				63A64C17E848147010CC9E90 /* Pods-private_Example.release.xcconfig */,
				F4D83241F9260C52700B8211 /* Pods-private_Tests.debug.xcconfig */,
				4436A5884454AE3826D63453 /* Pods-private_Tests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		6003F581195388D10070C39A = {
			isa = PBXGroup;
			children = (
				60FF7A9C1954A5C5007DD14C /* Podspec Metadata */,
				6003F593195388D20070C39A /* Example for private */,
				6003F5B5195388D20070C39A /* Tests */,
				6003F58C195388D20070C39A /* Frameworks */,
				6003F58B195388D20070C39A /* Products */,
				4EF402BA1F855CA7ABF5B713 /* Pods */,
			);
			sourceTree = "<group>";
		};
		6003F58B195388D20070C39A /* Products */ = {
			isa = PBXGroup;
			children = (
				6003F58A195388D20070C39A /* private_Example.app */,
				6003F5AE195388D20070C39A /* private_Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6003F58C195388D20070C39A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6003F58D195388D20070C39A /* Foundation.framework */,
				6003F58F195388D20070C39A /* CoreGraphics.framework */,
				6003F591195388D20070C39A /* UIKit.framework */,
				6003F5AF195388D20070C39A /* XCTest.framework */,
				A5382BD1AB1FC0382F193D4B /* Pods_private_Example.framework */,
				05095CCA7C4490E6D0559D98 /* Pods_private_Tests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6003F593195388D20070C39A /* Example for private */ = {
			isa = PBXGroup;
			children = (
				6003F59C195388D20070C39A /* HSWAppDelegate.h */,
				6003F59D195388D20070C39A /* HSWAppDelegate.m */,
				873B8AEA1B1F5CCA007FD442 /* Main.storyboard */,
				6003F5A5195388D20070C39A /* HSWViewController.h */,
				6003F5A6195388D20070C39A /* HSWViewController.m */,
				71719F9D1E33DC2100824A3D /* LaunchScreen.storyboard */,
				6003F5A8195388D20070C39A /* Images.xcassets */,
				6003F594195388D20070C39A /* Supporting Files */,
			);
			name = "Example for private";
			path = private;
			sourceTree = "<group>";
		};
		6003F594195388D20070C39A /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				6003F595195388D20070C39A /* private-Info.plist */,
				6003F596195388D20070C39A /* InfoPlist.strings */,
				6003F599195388D20070C39A /* main.m */,
				6003F59B195388D20070C39A /* private-Prefix.pch */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		6003F5B5195388D20070C39A /* Tests */ = {
			isa = PBXGroup;
			children = (
				6003F5BB195388D20070C39A /* Tests.m */,
				6003F5B6195388D20070C39A /* Supporting Files */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		6003F5B6195388D20070C39A /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				6003F5B7195388D20070C39A /* Tests-Info.plist */,
				6003F5B8195388D20070C39A /* InfoPlist.strings */,
				606FC2411953D9B200FFA9A0 /* Tests-Prefix.pch */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		60FF7A9C1954A5C5007DD14C /* Podspec Metadata */ = {
			isa = PBXGroup;
			children = (
				C032EC2197AE60559A2D1D66 /* private.podspec */,
				27FA7A80260337FABAA8BC57 /* README.md */,
				5A457BC08C603F8F4EDF7E7E /* LICENSE */,
			);
			name = "Podspec Metadata";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6003F589195388D20070C39A /* private_Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6003F5BF195388D20070C39A /* Build configuration list for PBXNativeTarget "private_Example" */;
			buildPhases = (
				280448993173FB2F699F1D75 /* [CP] Check Pods Manifest.lock */,
				6003F586195388D20070C39A /* Sources */,
				6003F587195388D20070C39A /* Frameworks */,
				6003F588195388D20070C39A /* Resources */,
				06E148A3A99696F3156C3D2D /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = private_Example;
			productName = private;
			productReference = 6003F58A195388D20070C39A /* private_Example.app */;
			productType = "com.apple.product-type.application";
		};
		6003F5AD195388D20070C39A /* private_Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6003F5C2195388D20070C39A /* Build configuration list for PBXNativeTarget "private_Tests" */;
			buildPhases = (
				64BB7CA5D472E9562F43EFD7 /* [CP] Check Pods Manifest.lock */,
				6003F5AA195388D20070C39A /* Sources */,
				6003F5AB195388D20070C39A /* Frameworks */,
				6003F5AC195388D20070C39A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				6003F5B4195388D20070C39A /* PBXTargetDependency */,
			);
			name = private_Tests;
			productName = privateTests;
			productReference = 6003F5AE195388D20070C39A /* private_Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6003F582195388D10070C39A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = HSW;
				LastUpgradeCheck = 0720;
				ORGANIZATIONNAME = "甘世伟";
				TargetAttributes = {
					6003F5AD195388D20070C39A = {
						TestTargetID = 6003F589195388D20070C39A;
					};
				};
			};
			buildConfigurationList = 6003F585195388D10070C39A /* Build configuration list for PBXProject "private" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 6003F581195388D10070C39A;
			productRefGroup = 6003F58B195388D20070C39A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6003F589195388D20070C39A /* private_Example */,
				6003F5AD195388D20070C39A /* private_Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6003F588195388D20070C39A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				873B8AEB1B1F5CCA007FD442 /* Main.storyboard in Resources */,
				71719F9F1E33DC2100824A3D /* LaunchScreen.storyboard in Resources */,
				6003F5A9195388D20070C39A /* Images.xcassets in Resources */,
				6003F598195388D20070C39A /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6003F5AC195388D20070C39A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F5BA195388D20070C39A /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		06E148A3A99696F3156C3D2D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-private_Example/Pods-private_Example-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/private/private.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/private.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-private_Example/Pods-private_Example-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		280448993173FB2F699F1D75 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-private_Example-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		64BB7CA5D472E9562F43EFD7 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-private_Tests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6003F586195388D20070C39A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F59E195388D20070C39A /* HSWAppDelegate.m in Sources */,
				6003F5A7195388D20070C39A /* HSWViewController.m in Sources */,
				6003F59A195388D20070C39A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6003F5AA195388D20070C39A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F5BC195388D20070C39A /* Tests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		6003F5B4195388D20070C39A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6003F589195388D20070C39A /* private_Example */;
			targetProxy = 6003F5B3195388D20070C39A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		6003F596195388D20070C39A /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				6003F597195388D20070C39A /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		6003F5B8195388D20070C39A /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				6003F5B9195388D20070C39A /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		71719F9D1E33DC2100824A3D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				71719F9E1E33DC2100824A3D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		6003F5BD195388D20070C39A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6003F5BE195388D20070C39A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6003F5C0195388D20070C39A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 40254962E09FF425E4BFA705 /* Pods-private_Example.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "private/private-Prefix.pch";
				INFOPLIST_FILE = "private/private-Info.plist";
				MODULE_NAME = ExampleApp;
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		6003F5C1195388D20070C39A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 63A64C17E848147010CC9E90 /* Pods-private_Example.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "private/private-Prefix.pch";
				INFOPLIST_FILE = "private/private-Info.plist";
				MODULE_NAME = ExampleApp;
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		6003F5C3195388D20070C39A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F4D83241F9260C52700B8211 /* Pods-private_Tests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Tests/Tests-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Tests/Tests-Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/private_Example.app/private_Example";
				WRAPPER_EXTENSION = xctest;
			};
			name = Debug;
		};
		6003F5C4195388D20070C39A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4436A5884454AE3826D63453 /* Pods-private_Tests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Tests/Tests-Prefix.pch";
				INFOPLIST_FILE = "Tests/Tests-Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/private_Example.app/private_Example";
				WRAPPER_EXTENSION = xctest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6003F585195388D10070C39A /* Build configuration list for PBXProject "private" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6003F5BD195388D20070C39A /* Debug */,
				6003F5BE195388D20070C39A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6003F5BF195388D20070C39A /* Build configuration list for PBXNativeTarget "private_Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6003F5C0195388D20070C39A /* Debug */,
				6003F5C1195388D20070C39A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6003F5C2195388D20070C39A /* Build configuration list for PBXNativeTarget "private_Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6003F5C3195388D20070C39A /* Debug */,
				6003F5C4195388D20070C39A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6003F582195388D10070C39A /* Project object */;
}
